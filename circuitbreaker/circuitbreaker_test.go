package circuitbreaker

import (
	"context"
	"errors"
	"sync"
	"testing"

	"github.com/myteksi/hystrix-go/hystrix"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"

	"gitlab.com/gx-regional/dakota/klient"
	"gitlab.com/gx-regional/dakota/klient/mocks"
	servusStatsD "gitlab.com/gx-regional/dakota/servus/v2/statsd"
)

func TestCircuitBreakerMiddleware(t *testing.T) {
	t.Run("NoError", func(t *testing.T) {
		happyService := "happy-service"
		maxConcurrency := 10
		cc := map[string]hystrix.CommandConfig{
			happyService: {
				Timeout:                100, // in ms
				MaxConcurrentRequests:  maxConcurrency,
				RequestVolumeThreshold: 2,
			},
		}

		var createApplicationsV2Descriptor = klient.EndpointDescriptor{
			Name:        "CreateApplicationsV2",
			Description: "",
			Method:      "POST",
			Path:        "/api/v2/applications",
		}
		clientCtx := klient.MakeContext(context.Background(), &createApplicationsV2Descriptor)
		mockRoundTripper := &mocks.RoundTripper{}
		mockRoundTripper.On("RoundTrip", mock.Anything, nil, nil).Run(func(args mock.Arguments) {
			_ = fib(5)
		}).Return(nil)

		middleware := NewMiddleware(happyService, cc, servusStatsD.NewNoop(), nil)
		hystrixRoundTripper := middleware.Apply(mockRoundTripper)

		runConcurrent(clientCtx, t, hystrixRoundTripper, maxConcurrency)
		mockRoundTripper.AssertExpectations(t)
	})

	t.Run("WithError", func(t *testing.T) {
		t.Run("With CircuitBreaker MaxConcurrency Error", func(t *testing.T) {
			cbService := "cb-service"
			exceededConcurrency := 10
			cc := map[string]hystrix.CommandConfig{
				cbService: {
					Timeout:                100, // in ms
					MaxConcurrentRequests:  1,
					RequestVolumeThreshold: 2,
				},
			}
			var createApplicationsV2Descriptor = klient.EndpointDescriptor{
				Name:        "CreateApplicationsV2",
				Description: "",
				Method:      "POST",
				Path:        "/api/v2/applications",
			}
			clientCtx := klient.MakeContext(context.Background(), &createApplicationsV2Descriptor)
			mockRoundTripper := &mocks.RoundTripper{}
			mockRoundTripper.On("RoundTrip", mock.Anything, nil, nil).Run(func(args mock.Arguments) {
				_ = fib(25)
			}).Return(nil)

			middleware := NewMiddleware(cbService, cc, servusStatsD.NewNoop(), nil)
			hystrixRoundTripper := middleware.Apply(mockRoundTripper)

			runConcurrent(clientCtx, t, hystrixRoundTripper, exceededConcurrency,
				hystrix.ErrCircuitOpen.Error(),
				hystrix.ErrMaxConcurrency.Error(),
				hystrix.ErrTimeout.Error(),
			)
			mockRoundTripper.AssertExpectations(t)
		})

		t.Run("With Service Error", func(t *testing.T) {
			errService := "err-service"
			maxConcurrency := 10
			cc := map[string]hystrix.CommandConfig{
				errService: {
					Timeout:                100, // in ms
					MaxConcurrentRequests:  maxConcurrency,
					RequestVolumeThreshold: 2,
				},
			}

			mockRoundTripper := &mocks.RoundTripper{}
			mockErr := errors.New("error returned by next RoundTripper")
			mockRoundTripper.On("RoundTrip", mock.Anything, nil, nil).Return(mockErr)
			var createApplicationsV2Descriptor = klient.EndpointDescriptor{
				Name:        "CreateApplicationsV2",
				Description: "",
				Method:      "POST",
				Path:        "/api/v2/applications",
			}
			clientCtx := klient.MakeContext(context.Background(), &createApplicationsV2Descriptor)
			middleware := NewMiddleware(errService, cc, servusStatsD.NewNoop(), nil)
			hystrixRoundTripper := middleware.Apply(mockRoundTripper)

			runConcurrent(clientCtx, t, hystrixRoundTripper, maxConcurrency,
				mockErr.Error(),
				hystrix.ErrCircuitOpen.Error(),
			)
			mockRoundTripper.AssertExpectations(t)
		})
	})
}

func fib(n int) int {
	if n == 0 || n == 1 {
		return 1
	}
	return fib(n-1) + fib(n-2)
}

func runConcurrent(ctx context.Context, t *testing.T, rt klient.RoundTripper, concurrency int, errStrs ...string) {
	wg := sync.WaitGroup{}
	for i := 0; i < concurrency; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			err := rt.RoundTrip(ctx, nil, nil)
			if err != nil {
				require.Contains(t, errStrs, err.Error())
			}
		}()
	}
	wg.Wait()
}
