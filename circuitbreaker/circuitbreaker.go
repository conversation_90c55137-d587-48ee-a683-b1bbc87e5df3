// Package circuitbreaker provides the circuitbreaker Middleware and RoundTripper.
package circuitbreaker

import (
	"context"
	"fmt"
	"time"

	dogstatsd "github.com/DataDog/datadog-go/statsd"

	"github.com/myteksi/hystrix-go/hystrix"

	"gitlab.com/gx-regional/dakota/klient"
	"gitlab.com/gx-regional/dakota/klient/utils"
	"gitlab.com/gx-regional/dakota/servus/v2"
	servusStatsD "gitlab.com/gx-regional/dakota/servus/v2/statsd"
	"gitlab.myteksi.net/gophers/go/commons/util/resilience/circuitbreaker"
)

// Middleware enhances klient.RoundTripper with logging capability.
type Middleware struct {
	serviceName    string
	circuitConfig  map[string]hystrix.CommandConfig
	statsDClient   servusStatsD.Client
	CBOptions      []circuitbreaker.Option
	CBStatsDClient *dogstatsd.Client
}

// NewMiddleware instantiates a new Middleware.
func NewMiddleware(serviceName string, config map[string]hystrix.CommandConfig, statsDClient servusStatsD.Client, CBOptions []circuitbreaker.Option) *Middleware {
	return &Middleware{
		serviceName:   serviceName,
		circuitConfig: config,
		statsDClient:  statsDClient,
		CBOptions:     CBOptions,
	}
}

// NewMiddlewareV2 instantiates a new Middleware.
func NewMiddlewareV2(serviceName string, config map[string]hystrix.CommandConfig, statsDClient servusStatsD.Client, CBOptions []circuitbreaker.Option, CBStatsD *dogstatsd.Client) *Middleware {
	return &Middleware{
		serviceName:    serviceName,
		circuitConfig:  config,
		statsDClient:   statsDClient,
		CBOptions:      CBOptions,
		CBStatsDClient: CBStatsD,
	}
}

// Apply wraps the klient.RoundTripper with logging logics.
func (m *Middleware) Apply(rt klient.RoundTripper) klient.RoundTripper {
	availableEndpointConfig := make(map[string]bool)
	for k, v := range m.circuitConfig {
		circuitbreaker.ConfigureCircuitV2(k,
			circuitbreaker.WithTimeoutMs(v.Timeout),
			circuitbreaker.WithErrorPercentageThreshold(v.ErrorPercentThreshold),
			circuitbreaker.WithMaxConcurrentRequests(v.MaxConcurrentRequests),
			circuitbreaker.WithRequestVolumeThreshold(v.RequestVolumeThreshold),
			circuitbreaker.WithSleepWindowMs(v.SleepWindow),
			circuitbreaker.WithQueueSizeRejectionThreshold(v.QueueSizeRejectionThreshold),
			circuitbreaker.WithCommandGroup(v.CommandGroup),
		)
		availableEndpointConfig[k] = true
	}
	circuitbreaker.SetupTurbineFromDatadogClient(m.CBStatsDClient)

	return &RoundTripper{
		serviceName:               m.serviceName,
		statsDClient:              m.statsDClient,
		next:                      rt,
		availableEndpointCBConfig: availableEndpointConfig,
		CBOptions:                 m.CBOptions,
	}
}

// RoundTripper logs the round-trip.
type RoundTripper struct {
	serviceName               string
	statsDClient              servusStatsD.Client
	next                      klient.RoundTripper
	availableEndpointCBConfig map[string]bool
	CBOptions                 []circuitbreaker.Option
}

// RoundTrip invokes the next RoundTripper in the chain and complies with the configured circuit-breaker
func (r *RoundTripper) RoundTrip(ctx context.Context, req klient.Request, res klient.Response) error {
	desc := klient.DescribeEndpoint(ctx)
	statsTags := append(make([]string, 0), "circuit_name:"+r.serviceName+"_"+desc.Name)
	statsTags = append(statsTags, "to_service:"+r.serviceName)
	statsTags = append(statsTags, "endpoint:"+desc.Name)
	sourceServiceName := utils.GetSourceServiceName()
	if sourceServiceName != "" {
		statsTags = append(statsTags, "from_service:"+sourceServiceName)
	}
	callCtx, cancel := context.WithCancel(ctx)

	defer cancel()
	defer func(start time.Time) {
		r.statsDClient.Duration("klient.circuit", "latency", start, statsTags...)
	}(time.Now())

	circuitErr := circuitbreaker.Do(ctx, r.getCircuitKey(desc.Name), func() error {
		return r.next.RoundTrip(callCtx, req, res)
	}, r.CBOptions...)

	if circuitErr != nil {
		statsTags = append(statsTags, "error:"+circuitErr.Error())
		if apiError, ok := circuitErr.(servus.APIError); ok {
			httpCode, _ := apiError.Info()
			statsTags = append(statsTags, fmt.Sprintf("%s:%d", "httpCode", httpCode))
		}
	} else {
		statsTags = append(statsTags, "error:nil")
	}

	return circuitErr
}

// get circuit-breaker key for service's endpoints
func (r *RoundTripper) getCircuitKey(endpointName string) string {
	if r.availableEndpointCBConfig[fmt.Sprintf("%s.%s", r.serviceName, endpointName)] {
		return fmt.Sprintf("%s.%s", r.serviceName, endpointName)
	}
	return r.serviceName
}
