package klient

import (
	"context"
)

// EndpointDescriptor consists of metadata about an endpoint that will be useful to middleware.
type EndpointDescriptor struct {
	Service     string
	Name        string
	Description string
	Method      string
	Path        string
}

var contextKey = &struct{}{}

// MakeContext makes context that includes information that is useful to middleware.
func MakeContext(ctx context.Context, endptDesc *EndpointDescriptor) context.Context {
	return context.WithValue(ctx, contextKey, endptDesc)
}

// DescribeEndpoint retrieves endpoint metadata from context.
func DescribeEndpoint(ctx context.Context) *EndpointDescriptor {
	value := ctx.Value(contextKey)
	if value == nil {
		return nil
	}
	return value.(*EndpointDescriptor)
}
