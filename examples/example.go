package examples

import (
	"context"
	"net/http"

	"gitlab.com/gx-regional/dakota/klient"
)

// Client is an example client implemented by Klient runtime components.
type Client struct {
	machinery klient.RoundTripper
}

// MakeClient instantiates a new Client.
func MakeClient(initializer klient.Initializer) (*Client, error) {
	machinery, err := initializer.Initialize()
	if err != nil {
		return nil, err
	}
	return &Client{
		machinery: machinery,
	}, nil
}

// Foo is an example.
func (c *Client) Foo(ctx context.Context, req *Bar) (*Baz, error) {
	res := &Baz{}
	ctx = klient.MakeContext(ctx, fooDescriptor)
	return res, c.machinery.RoundTrip(ctx, req, res)
}

// Bar is an example request.
type Bar struct{}

// EncodeHTTPRequest encodes a http.Request, implements klient.Request.
func (b *Bar) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	return http.NewRequest(http.MethodGet, baseURL+"/foo", nil)
}

// Baz is an example response.
type Baz struct{}

// DecodeHTTPResponse decodes from a http.Response, implements klient.Response.
func (c *Baz) DecodeHTTPResponse(res *http.Response) error {
	return nil
}

var fooDescriptor = &klient.EndpointDescriptor{
	Service:     "Examples",
	Name:        "Foo",
	Description: "Foo is an example.",
	Path:        "/foo",
}
