package examples

import (
	"context"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/stretchr/testify/require"

	"gitlab.com/gx-regional/dakota/klient/initialize"
	servusStatsD "gitlab.com/gx-regional/dakota/servus/v2/statsd"
	"gitlab.myteksi.net/gophers/go/commons/util/log/yall/slog"
)

func TestExampleClient(t *testing.T) {
	testServer := httptest.NewServer(
		http.HandlerFunc(
			func(res http.ResponseWriter, req *http.Request) {
				res.WriteHeader(200)
			},
		),
	)
	defer testServer.Close()

	httpClient := &http.Client{
		Transport: &http.Transport{
			MaxConnsPerHost: 255,
			IdleConnTimeout: 60 * time.Second,
		},
		Timeout: 5 * time.Second,
	}

	logger, _, err := slog.Development()
	require.NoError(t, err)

	client, err := MakeClient(initialize.Using{
		HTTPClient:   httpClient,
		BaseURL:      testServer.URL,
		Logger:       logger,
		StatsDClient: servusStatsD.NewNoop(),
	})
	require.NoError(t, err)

	baz, err := client.Foo(context.Background(), &Bar{})
	require.NoError(t, err)
	require.NotNil(t, baz)
}
