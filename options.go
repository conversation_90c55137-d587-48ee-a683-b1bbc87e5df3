package klient

import (
	"crypto/tls"
	"net/http"

	dogstatsd "github.com/DataDog/datadog-go/statsd"
	"github.com/myteksi/hystrix-go/hystrix"

	"gitlab.com/gx-regional/dakota/common/tracing"
	servusStatsD "gitlab.com/gx-regional/dakota/servus/v2/statsd"
	"gitlab.myteksi.net/gophers/go/commons/util/log/yall/slog"
	"gitlab.myteksi.net/gophers/go/commons/util/resilience/circuitbreaker"
)

// Option is functional config to create the new Client
type Option func(config *Config)

// Config is group of all the options supported
type Config struct {
	ServiceName string
	// For rest.RoundTripper, required.
	HTTPClient *http.Client
	BaseURL    string
	// For logging.Middleware, optional.
	Logger slog.YallLogger
	// For circuit breaker, optional.
	CircuitConfig map[string]hystrix.CommandConfig
	// For Circuit Breaker options
	CBOptions []circuitbreaker.Option
	// for sending stats to datadog.
	StatsDClient servusStatsD.Client
	// For tracing.Middleware, optional
	Tracer tracing.Tracer
	// For tls.Config, optional
	TLSConfig *tls.Config
	// For scheduled dependency health check, by default health check is required
	NoHealthCheck bool
	// For internal services, propagate common headers
	PropagateCommonHeaders bool
	// CBStatsD ... statsd for cb
	CBStatsD *dogstatsd.Client
	// EnableMockService only non-prod, use mock-service or not, optional
	EnableMockService bool
}

// WithHTTPClient set the http client that should be used
func WithHTTPClient(client *http.Client) Option {
	return func(config *Config) {
		config.HTTPClient = client
	}
}

// WithServiceName ...
func WithServiceName(name string) Option {
	return func(config *Config) {
		config.ServiceName = name
	}
}

// WithEnableMockService ...
func WithEnableMockService(enableMockService bool) Option {
	return func(config *Config) {
		config.EnableMockService = enableMockService
	}
}

// WithCircuitConfig ...
func WithCircuitConfig(circuitConfig map[string]hystrix.CommandConfig) Option {
	return func(config *Config) {
		config.CircuitConfig = circuitConfig
	}
}

// WithCircuitOptions ...
func WithCircuitOptions(cbOptions []circuitbreaker.Option) Option {
	return func(config *Config) {
		config.CBOptions = cbOptions
	}
}

// WithLogger ...
func WithLogger(logger slog.YallLogger) Option {
	return func(config *Config) {
		config.Logger = logger
	}
}

// WithTracing ...
func WithTracing(tracer tracing.Tracer) Option {
	return func(config *Config) {
		config.Tracer = tracer
	}
}

// WithStatsDClient ...
func WithStatsDClient(statsdclient servusStatsD.Client) Option {
	return func(config *Config) {
		config.StatsDClient = statsdclient
	}
}

// WithTLSConfig ...
func WithTLSConfig(tlsConfig *tls.Config) Option {
	return func(config *Config) {
		config.TLSConfig = tlsConfig
	}
}

// WithoutHealthCheck ...
func WithoutHealthCheck() Option {
	return func(config *Config) {
		config.NoHealthCheck = true
	}
}

// PropagateCommonHeaders ...
func PropagateCommonHeaders() Option {
	return func(config *Config) {
		config.PropagateCommonHeaders = true
	}
}

// WithStatsDForCB ...
func WithStatsDForCB(statsD *dogstatsd.Client) Option {
	return func(config *Config) {
		config.CBStatsD = statsD
	}
}
