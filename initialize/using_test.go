package initialize

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/require"

	servusStatsD "gitlab.com/gx-regional/dakota/servus/v2/statsd"
	"gitlab.myteksi.net/gophers/go/commons/util/log/yall/slog"
)

func TestUsing(t *testing.T) {
	t.Run("MissingRequiredValues", func(t *testing.T) {
		_, err := Using{}.Initialize()
		require.Error(t, err)
	})
	t.Run("AllValuesPresent", func(t *testing.T) {
		logger, _, err := slog.Development()
		require.NoError(t, err)

		rt, err := Using{
			BaseURL:      "http://nothing:8080",
			HTTPClient:   http.DefaultClient,
			Logger:       logger,
			StatsDClient: servusStatsD.NewNoop(),
		}.Initialize()

		require.NoError(t, err)
		require.NotNil(t, rt)
	})
}
