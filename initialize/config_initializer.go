// Package initialize provides mechanism to initialize the client with configs and dependencies.
package initialize

import (
	"fmt"
	"net"
	"net/http"
	"runtime"
	"time"

	"gitlab.com/gx-regional/dakota/klient"
	"gitlab.com/gx-regional/dakota/klient/circuitbreaker"
	"gitlab.com/gx-regional/dakota/klient/logging"
	"gitlab.com/gx-regional/dakota/klient/mockservice"
	"gitlab.com/gx-regional/dakota/klient/rest"
	"gitlab.com/gx-regional/dakota/klient/tracing"
	servusStatsD "gitlab.com/gx-regional/dakota/servus/v2/statsd"
)

const (
	// DefaultHTTPTimeout specifies a time limit for requests made by this
	// Client. The timeout includes connection time, any
	// redirects, and reading the response body. The timer remains
	// running after Get, Head, Post, or Do return and will
	// interrupt reading of the Response.Body.
	DefaultHTTPTimeout = 30 * time.Second

	// DefaultHTTPDialKeepAlive specifies the interval between keep-alive
	// probes for an active network connection.
	DefaultHTTPDialKeepAlive = 30 * time.Second

	// DefaultHTTPDialTimeout is the maximum amount of time a dial will wait for
	// a connect to complete.
	DefaultHTTPDialTimeout = 30 * time.Second

	// DefaultHTTPMaxIdleConnections controls the maximum number of idle (keep-alive)
	// connections across all hosts.
	DefaultHTTPMaxIdleConnections = 100

	// DefaultHTTPIdleConnTimeout is the maximum amount of time an idle
	// (keep-alive) connection will remain idle before closing
	// itself.
	DefaultHTTPIdleConnTimeout = 90 * time.Second

	// DefaultHTTPTLSTimeout specifies the maximum amount of time waiting to
	// wait for a TLS handshake.
	DefaultHTTPTLSTimeout = 10 * time.Second

	// DefaultHTTPExpectContTimeout specifies the amount of
	// time to wait for a server's first response headers after fully
	// writing the request headers if the request has an
	// "Expect: 100-continue" header.
	DefaultHTTPExpectContTimeout = 1 * time.Second
)

// configInitializer is the implementation of klient.Initializer interface. It operates based on default config or the
// user provided configuration
type configInitializer struct {
	config *klient.Config
}

// New creates Initializer using provided baseURL and optional list of options.
// If there is a default value for a particular config then that will used if option is not provided.
func New(baseURL string, options ...klient.Option) klient.Initializer {
	var config = defaultConfig()

	// Apply options
	config.BaseURL = baseURL
	for _, o := range options {
		o(&config)
	}

	return NewWithConfig(&config)
}

// NewWithConfig creates Initializer with provided config, No defaults used here
func NewWithConfig(config *klient.Config) klient.Initializer {
	return &configInitializer{
		config: config,
	}
}

func (c *configInitializer) Initialize() (klient.RoundTripper, error) {
	if c.config.BaseURL == "" {
		return nil, fmt.Errorf("BaseURL is required to initialize client")
	}

	if c.config.CircuitConfig != nil && c.config.ServiceName == "" {
		return nil, fmt.Errorf("serviceName is required to configure circuit breaker")
	}

	statsClient := c.config.StatsDClient
	if statsClient == nil {
		statsClient = servusStatsD.NewNoop()
	}
	var rt klient.RoundTripper = rest.NewRoundTripper(c.config.HTTPClient, c.config.BaseURL, c.config.Logger, c.config.TLSConfig, !c.config.NoHealthCheck, c.config.PropagateCommonHeaders, statsClient)

	if c.config.EnableMockService {
		if c.config.Logger != nil {
			c.config.Logger.Info("MOCK_SERVICE_ENABLED", fmt.Sprintf("EnableMockService For Test serviceName=%s baseURL=%s\n", c.config.ServiceName, c.config.BaseURL))
		}
		rt = mockservice.NewMiddleware(c.config.HTTPClient, c.config.BaseURL, c.config.Logger, c.config.PropagateCommonHeaders, statsClient).Apply(rt)
	}
	if c.config.Logger != nil {
		rt = logging.NewMiddleware(c.config.Logger).Apply(rt)
	}

	if c.config.CircuitConfig != nil {
		rt = circuitbreaker.NewMiddlewareV2(c.config.ServiceName, c.config.CircuitConfig, statsClient, c.config.CBOptions, c.config.CBStatsD).Apply(rt)
	}

	if c.config.Tracer != nil {
		rt = tracing.NewMiddleware(c.config.Tracer).Apply(rt)
	}

	return rt, nil
}

// defaultConfig returns the config object with default values
func defaultConfig() klient.Config {
	return klient.Config{
		HTTPClient: defaultHTTPClient(),
	}
}

func defaultHTTPClient() *http.Client {
	dialer := &net.Dialer{
		Timeout:   DefaultHTTPDialTimeout,
		KeepAlive: DefaultHTTPDialKeepAlive,
	}

	transport := &http.Transport{
		Proxy:                 http.ProxyFromEnvironment,
		DialContext:           dialer.DialContext,
		MaxIdleConns:          DefaultHTTPMaxIdleConnections,
		MaxIdleConnsPerHost:   runtime.GOMAXPROCS(0) + 1, // Get the current Max CPUs and add one that
		IdleConnTimeout:       DefaultHTTPIdleConnTimeout,
		TLSHandshakeTimeout:   DefaultHTTPTLSTimeout,
		ForceAttemptHTTP2:     true,
		ExpectContinueTimeout: DefaultHTTPExpectContTimeout,
		TLSClientConfig:       nil,
	}

	return &http.Client{
		Transport: transport,
		Timeout:   DefaultHTTPTimeout,
	}
}
