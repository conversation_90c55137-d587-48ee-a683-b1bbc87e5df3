package initialize

import (
	"net/http"
	"runtime"
	"testing"

	"github.com/myteksi/hystrix-go/hystrix"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gitlab.com/gx-regional/dakota/common/tracing"
	"gitlab.com/gx-regional/dakota/klient"
	"gitlab.myteksi.net/gophers/go/commons/util/log/yall/slog"
)

func TestInitializer(t *testing.T) {
	t.Run("MissingRequiredValues", func(t *testing.T) {
		_, err := NewWithConfig(&klient.Config{}).Initialize()
		require.Error(t, err)
	})

	t.Run("AllValuesPresent", func(t *testing.T) {
		logger, _, err := slog.Development()
		tracer := tracing.DefaultTracer()
		require.NoError(t, err)

		rt, err := New(
			"http://nothing:8080",
			klient.WithHTTPClient(http.DefaultClient),
			klient.WithLogger(logger),
			klient.WithTracing(tracer),
			klient.WithServiceName("TestService"),
			klient.WithEnableMockService(true),
			klient.WithCircuitConfig(map[string]hystrix.CommandConfig{
				"config": {},
			}),
		).Initialize()

		require.NoError(t, err)
		require.NotNil(t, rt)
	})

	t.Run("servicelevelcb", func(t *testing.T) {
		logger, _, err := slog.Development()
		tracer := tracing.DefaultTracer()
		require.NoError(t, err)

		rt, err := New(
			"http://nothing:8080",
			klient.WithHTTPClient(http.DefaultClient),
			klient.WithLogger(logger),
			klient.WithTracing(tracer),
			klient.WithServiceName("TestService"),
			klient.WithCircuitConfig(map[string]hystrix.CommandConfig{
				"config": {},
			}),
		).Initialize()

		require.NoError(t, err)
		require.NotNil(t, rt)
	})

	t.Run("VerifyDefaultHttpClient", func(t *testing.T) {
		initializer := New(
			"http://nothing:8080",
		)
		configInitializer := initializer.(*configInitializer)
		assert.True(t, validateDefaultHTTPClient(configInitializer.config.HTTPClient), "Http Client Default values mismatched")
	})
}

func validateDefaultHTTPClient(client *http.Client) bool {
	transport := client.Transport.(*http.Transport)
	if client.Timeout == DefaultHTTPTimeout &&
		transport.ForceAttemptHTTP2 == true &&
		transport.TLSHandshakeTimeout == DefaultHTTPTLSTimeout &&
		transport.IdleConnTimeout == DefaultHTTPIdleConnTimeout &&
		transport.MaxIdleConnsPerHost == runtime.GOMAXPROCS(0)+1 &&
		transport.MaxIdleConns == DefaultHTTPMaxIdleConnections &&
		transport.ExpectContinueTimeout == DefaultHTTPExpectContTimeout {
		return true
	}

	return false
}
