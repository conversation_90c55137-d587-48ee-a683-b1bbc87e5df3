// Package initialize provides mechanism to initialize the client with configs and dependencies.
package initialize

import (
	"fmt"
	"net/http"

	"github.com/myteksi/hystrix-go/hystrix"

	"gitlab.com/gx-regional/dakota/klient"
	"gitlab.com/gx-regional/dakota/klient/circuitbreaker"
	"gitlab.com/gx-regional/dakota/klient/logging"
	"gitlab.com/gx-regional/dakota/klient/rest"
	servusStatsD "gitlab.com/gx-regional/dakota/servus/v2/statsd"
	"gitlab.myteksi.net/gophers/go/commons/util/log/yall/slog"
	cb "gitlab.myteksi.net/gophers/go/commons/util/resilience/circuitbreaker"
)

// Using initializes the client using the specified configs and dependencies, it abstracts user away from the underlying RoundTripper and Middleware.
// It initializes the client according to the defined fields, leaving fields to be nil could omit some optional middlewares.
//
// Deprecated: Use New(baseURL string, options ...Option) klient.Initializer or NewWithConfig(config *Config) klient.Initializer instead
type Using struct {
	ServiceName string
	// For rest.RoundTripper, required.
	HTTPClient *http.Client
	BaseURL    string
	// For logging.Middleware, optional.
	Logger slog.YallLogger
	// For circuit breaker, optional.
	CircuitConfig map[string]hystrix.CommandConfig
	// for sending stats to datadog.
	StatsDClient servusStatsD.Client
	// For endpoint level Circuit config.
	EndpointLevelCB bool
	// For Circuit Breaker options
	CBOptions []cb.Option
}

// Initialize implements klient.Initializer, composes configs and dependencies to form a functional RoundTripper.
func (u Using) Initialize() (klient.RoundTripper, error) {
	statsClient := u.StatsDClient
	if statsClient == nil {
		statsClient = servusStatsD.NewNoop()
	}

	if u.HTTPClient == nil || u.BaseURL == "" {
		return nil, fmt.Errorf("both HTTPClient and BaseURL are required to initialize client")
	}
	if u.CircuitConfig != nil && u.ServiceName == "" {
		return nil, fmt.Errorf("serviceName is required to configure circuit breaker")
	}
	var rt klient.RoundTripper = rest.NewRoundTripper(u.HTTPClient, u.BaseURL, u.Logger, nil, true, true, statsClient)

	if u.Logger != nil {
		rt = logging.NewMiddleware(u.Logger).Apply(rt)
	}

	if u.CircuitConfig != nil {
		rt = circuitbreaker.NewMiddleware(u.ServiceName, u.CircuitConfig, statsClient, u.CBOptions).Apply(rt)
	}

	return rt, nil
}
