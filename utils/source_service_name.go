// Package utils provides utility functions for the package.
package utils

import (
	"os"
	"strings"
	"sync"
)

var (
	once              sync.Once
	sourceServiceName string
)

// GetSourceServiceName returns the source service name from the pod name.
func GetSourceServiceName() string {
	once.Do(func() {
		podName, found := os.LookupEnv("HOSTNAME")
		if found {
			podNameData := strings.Split(podName, "-")
			if len(podNameData) > 2 {
				podNameData = podNameData[:len(podNameData)-2]
				sourceServiceName = strings.Join(podNameData, "-")
			}
		}
	})
	return sourceServiceName
}
