package errorhandling

import (
	"bytes"
	"encoding/json"
	"errors"
	"io/ioutil"
	"net/http"
	"testing"

	"github.com/stretchr/testify/require"
)

func Test_unmarshalError(t *testing.T) {
	t.Run("happy-path", func(t *testing.T) {
		testBody := Error{
			Code:    "userNotFound",
			Message: "The user is not found",
			Errors: []ErrorDetail{
				{
					ErrorCode: "invalidField",
					Path:      "name",
					Message:   "The field is invalid",
				},
				{
					ErrorCode: "unexpectedValue",
					Path:      "age",
					Message:   "The value is unexpected",
				},
			},
		}

		respBytes, _ := json.Marshal(testBody)

		testResp := &http.Response{
			StatusCode: http.StatusNotFound,
			Body:       ioutil.NopCloser(bytes.NewReader(respBytes)),
		}

		err := UnmarshalError(testResp)
		require.IsType(t, &Error{}, err)
		require.Equal(t, `userNotFound:The user is not found:[{"errorCode":"invalidField","message":"The field is invalid","path":"name"},{"errorCode":"unexpectedValue","message":"The value is unexpected","path":"age"}]`, err.Error())
	})

	t.Run("error-json-marshal", func(t *testing.T) {
		respBytes, _ := json.Marshal("")

		testResp := &http.Response{
			StatusCode: http.StatusBadRequest,
			Body:       ioutil.NopCloser(bytes.NewReader(respBytes)),
		}

		err := UnmarshalError(testResp)
		require.Error(t, err)
		require.EqualError(t, err, "GENERIC_ERROR:err_decode=readObjectStart: expect { or n, but found \", error found in #1 byte of ...|\"\"|..., bigger context ...|\"\"|...")
	})

	t.Run("error-text-body", func(t *testing.T) {
		respBytes, _ := json.Marshal("context canceled")

		testResp := &http.Response{
			StatusCode: http.StatusInternalServerError,
			Body:       ioutil.NopCloser(bytes.NewReader(respBytes)),
		}

		err := UnmarshalError(testResp)
		require.Error(t, err)
		require.EqualError(t, err, "GENERIC_ERROR:err=\"context canceled\"")
	})

	t.Run("error-reader", func(t *testing.T) {
		testResp := &http.Response{
			StatusCode: http.StatusInternalServerError,
			Body:       errReader(0),
		}

		err := UnmarshalError(testResp)
		require.Error(t, err)
		require.EqualError(t, err, "GENERIC_ERROR:read_err=simulate error")
	})
}

type errReader int

func (errReader) Read(p []byte) (n int, err error) {
	return 0, errors.New("simulate error")
}

func (errReader) Close() error {
	return nil
}

func Test_HTTPErrorCode(t *testing.T) {
	var testGoodFunc = func() error {
		return &Error{
			HTTPCode: http.StatusNotFound,
		}
	}

	var testBadFunc = func() error {
		return errors.New("some error")
	}

	httpCode := HTTPErrorCode(testGoodFunc())
	require.Equal(t, httpCode, http.StatusNotFound)

	httpCode = HTTPErrorCode(testBadFunc())
	require.Equal(t, httpCode, http.StatusInternalServerError)
}

func Test_ErrorCode(t *testing.T) {
	var testGoodFunc = func() error {
		return &Error{
			HTTPCode: http.StatusNotFound,
			Code:     "invalidFieldValue",
		}
	}

	var testBadFunc = func() error {
		return errors.New("some error")
	}

	errorCode := ErrorCode(testGoodFunc())
	require.Equal(t, errorCode, "invalidFieldValue")

	errorCode = ErrorCode(testBadFunc())
	require.Equal(t, errorCode, "some error")
}
