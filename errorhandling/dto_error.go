package errorhandling

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
)

// Headers
const (
	headerContentType = "Content-Type"
)

// Scheme
const (
	jsonScheme = "application/json"
)

// Error ...
type Error struct {
	HTTPCode int           `json:"-"`
	Code     string        `json:"code,omitempty"`
	Message  string        `json:"message,omitempty"`
	Errors   []ErrorDetail `json:"errors,omitempty"`
}

// ErrorDetail provides more details e.g list of fields that are invalid
type ErrorDetail struct {
	ErrorCode string `json:"errorCode,omitempty"`
	Message   string `json:"message,omitempty"`
	Path      string `json:"path,omitempty"`
}

// Error implements the Error interface.
func (e *Error) Error() string {
	var b strings.Builder
	b.Grow(100)
	fmt.Fprintf(&b, "%s:%s", e.Code, e.Message)

	if len(e.Errors) != 0 {
		b.<PERSON><PERSON>(200)
		details, err := json.Marshal(e.Errors)
		if err != nil {
			return b.String()
		}
		b.WriteString(fmt.Sprintf(":%s", details))
	}

	return b.String()
}

// Info implements servus.APIError
func (e Error) Info() (statusCode int, code string) {
	return e.HTTPCode, e.Code
}

// ToHTTPResponse implements servus.CustomHTTPResponse
func (e Error) ToHTTPResponse(w http.ResponseWriter) error {
	w.Header().Add(headerContentType, jsonScheme)
	w.WriteHeader(e.HTTPCode)
	enc := json.NewEncoder(w)
	return enc.Encode(e)
}
