// Package errorhandling helps to unmarshal an errorhandling from the service into a standard dto
package errorhandling

import (
	"fmt"
	"io"
	"net/http"
	"strings"

	jsoniter "github.com/json-iterator/go"
)

const (
	// ErrGeneric defines the error code for generic error.
	ErrGeneric = "GENERIC_ERROR"
)

// UnmarshalError unmarshalls response into an Error DTO.
func UnmarshalError(resp *http.Response) error {
	defer func() {
		_ = resp.Body.Close()
	}()

	if resp.StatusCode >= 500 {
		return &Error{
			HTTPCode: resp.StatusCode,
			Code:     ErrGeneric,
			Message:  readGenericBody(resp.Body),
		}
	}

	var klientErr Error
	err := jsoniter.NewDecoder(resp.Body).Decode(&klientErr)
	if err != nil {
		return &Error{
			HTTPCode: resp.StatusCode,
			Code:     ErrGeneric,
			Message:  fmt.Sprintf("err_decode=%s", err.<PERSON>r()),
		}
	}

	klientErr.HTTPCode = resp.StatusCode
	return &klientErr
}

func readGenericBody(reader io.Reader) string {
	var msg []string
	data, err := io.ReadAll(reader)
	if err != nil {
		msg = append(msg, "read_err")
		if err.Error() != "" {
			msg = append(msg, err.Error())
		}

		return strings.Join(msg, "=")
	}

	resp := "N/A"
	if len(data) > 0 {
		resp = string(data)
	}
	msg = append(msg, "err", resp)

	return strings.Join(msg, "=")
}

// HTTPErrorCode returns http error code from an error message
func HTTPErrorCode(err error) int {
	klientErr, ok := err.(*Error)
	if !ok {
		return http.StatusInternalServerError
	}
	return klientErr.HTTPCode
}

// ErrorCode returns specific error code from an error message
func ErrorCode(err error) string {
	klientErr, ok := err.(*Error)
	if !ok {
		return err.Error()
	}
	return klientErr.Code
}
