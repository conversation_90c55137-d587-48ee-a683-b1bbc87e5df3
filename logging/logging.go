// Package logging provides the logging Middleware and RoundTripper.
package logging

import (
	"context"
	"time"

	"gitlab.myteksi.net/gophers/go/commons/util/log/yall/slog"
	"gitlab.myteksi.net/gophers/go/commons/util/tags"
	"gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"

	"gitlab.com/gx-regional/dakota/klient"
)

// Middleware enhances klient.RoundTripper with logging capability.
type Middleware struct {
	logger slog.YallLogger
}

// NewMiddleware instantiates a new Middleware.
func NewMiddleware(logger slog.YallLogger) *Middleware {
	return &Middleware{
		logger: logger,
	}
}

// Apply wraps the klient.RoundTripper with logging logics.
func (m *Middleware) Apply(rt klient.RoundTripper) klient.RoundTripper {
	return &RoundTripper{
		logger: m.logger,
		next:   rt,
	}
}

// RoundTripper logs the round-trip.
type RoundTripper struct {
	logger slog.YallLogger
	next   klient.RoundTripper
}

// RoundTrip invokes the next RoundTripper in the chain and then logs the endpoint metadata, elapsed time, and error status in Common Grab Log Schema.
func (r *RoundTripper) RoundTrip(ctx context.Context, req klient.Request, res klient.Response) error {
	desc := klient.DescribeEndpoint(ctx)
	begin := time.Now()

	err := r.next.RoundTrip(ctx, req, res)

	elapsed := time.Since(begin)

	tags := []tags.Tag{
		common.Service(desc.Service),
		common.Endpoint(desc.Name),
		common.EndpointElapsed(int64(elapsed / time.Microsecond)),
		common.Error(maybeNil(err)),
	}

	if err != nil {
		r.logger.Warn(loggingEvent, callEndptMsg, tags...)
	} else {
		r.logger.Info(loggingEvent, callEndptMsg, tags...)
	}

	return err
}

const (
	loggingEvent = "client.middleware.logging"
	callEndptMsg = "call endpoint"
)

func maybeNil(err error) string {
	if err != nil {
		return err.Error()
	}
	return "<nil>"
}
