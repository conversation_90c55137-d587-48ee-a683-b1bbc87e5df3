package logging

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"

	"gitlab.com/gx-regional/dakota/klient"
	"gitlab.com/gx-regional/dakota/klient/mocks"
	"gitlab.myteksi.net/gophers/go/commons/util/log/yall/slog"
)

func TestLoggingMiddleware(t *testing.T) {
	t.Run("NoError", func(t *testing.T) {
		mockLogger := &slog.MockYallLogger{}
		mockRoundTripper := &mocks.RoundTripper{}

		middleware := NewMiddleware(mockLogger)
		loggedRoundTripper := middleware.Apply(mockRoundTripper)
		endptDesc := &klient.EndpointDescriptor{}
		ctx := klient.MakeContext(context.Background(), endptDesc)

		mockRoundTripper.On("RoundTrip", ctx, nil, nil).Return(nil)
		mockLogger.On("Info", loggingEvent, callEndptMsg, mock.Anything, mock.Anything, mock.Anything, mock.Anything)

		err := loggedRoundTripper.RoundTrip(ctx, nil, nil)
		require.NoError(t, err)

		mockLogger.AssertExpectations(t)
		mockRoundTripper.AssertExpectations(t)
	})
	t.Run("WithError", func(t *testing.T) {
		mockLogger := &slog.MockYallLogger{}
		mockRoundTripper := &mocks.RoundTripper{}

		middleware := NewMiddleware(mockLogger)
		loggedRoundTripper := middleware.Apply(mockRoundTripper)
		endptDesc := &klient.EndpointDescriptor{}
		ctx := klient.MakeContext(context.Background(), endptDesc)

		mockRoundTripper.On("RoundTrip", ctx, nil, nil).Return(errors.New("error returned by next RoundTripper"))
		mockLogger.On("Warn", loggingEvent, callEndptMsg, mock.Anything, mock.Anything, mock.Anything, mock.Anything)

		err := loggedRoundTripper.RoundTrip(ctx, nil, nil)
		require.Error(t, err)

		mockLogger.AssertExpectations(t)
		mockRoundTripper.AssertExpectations(t)
	})
}

func TestMaybeNil(t *testing.T) {
	err := errors.New("this is an error")
	require.Equal(t, err.Error(), maybeNil(err))

	require.Equal(t, "<nil>", maybeNil(nil))
}
