// Package klient provides common interfaces and structs for client implementation.
package klient

import (
	"context"
	"net/http"
)

//go:generate mockery --name=Request|Response|RoundTripper|Middleware

// Request models a request object involves in a request-response round trip.
// It converts the plain object into protocol specific message.
type Request interface {
	EncodeHTTPRequest(baseURL string) (*http.Request, error)
}

// Response models a response object involves in a request-response round trip.
// It converts the protocol specific message into plain object
type Response interface {
	DecodeHTTPResponse(res *http.Response) error
}

// RoundTripper is the basic unit in the middlewares chain.
type RoundTripper interface {
	RoundTrip(ctx context.Context, req Request, res Response) error
}

// Middleware wraps RoundTripper to bring in more features/functionalities.
type Middleware interface {
	Apply(rt RoundTripper) RoundTripper
}

// Initializer initializes a functional RoundTripper with configs and dependencies.
type Initializer interface {
	Initialize() (RoundTripper, error)
}
