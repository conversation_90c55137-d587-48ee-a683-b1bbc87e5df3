// Package tracing provides the tracing Middleware and RoundTripper.
package tracing

import (
	"context"
	"net/http"

	"gopkg.in/DataDog/dd-trace-go.v1/ddtrace/tracer"

	"gitlab.com/gx-regional/dakota/common/tracing"
	"gitlab.com/gx-regional/dakota/klient"
)

type requestTr struct {
	req    klient.Request
	ctx    context.Context
	tracer tracing.Tracer
}

// EncodeHTTPRequest ...
func (t *requestTr) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	httpReq, err := t.req.EncodeHTTPRequest(baseURL)
	if err != nil {
		return nil, err
	}

	span, isSpan := t.tracer.SpanFromContext(t.ctx)
	if !isSpan {
		span = t.tracer.StartSpan("[klient]: " + baseURL)
	}

	err = t.tracer.InjectSpanContext(span.Context(), tracer.HTTPHeadersCarrier(httpReq.Header))

	return httpReq, err
}

// Middleware enhances klient.RoundTripper with logging capability.
type Middleware struct {
	tracer tracing.Tracer
}

// NewMiddleware instantiates a new Middleware.
func NewMiddleware(tracer tracing.Tracer) *Middleware {
	return &Middleware{
		tracer: tracer,
	}
}

// Apply wraps the klient.RoundTripper with logging logics.
func (m *Middleware) Apply(rt klient.RoundTripper) klient.RoundTripper {
	return &RoundTripper{
		tracer: m.tracer,
		next:   rt,
	}
}

// RoundTripper logs the round-trip.
type RoundTripper struct {
	tracer tracing.Tracer
	next   klient.RoundTripper
}

// RoundTrip invokes the next RoundTripper in the chain and then logs the endpoint metadata, elapsed time, and error status in Common Grab Log Schema.
func (r *RoundTripper) RoundTrip(ctx context.Context, req klient.Request, res klient.Response) error {
	reqWithTracer := &requestTr{
		ctx:    ctx,
		req:    req,
		tracer: r.tracer,
	}

	return r.next.RoundTrip(ctx, reqWithTracer, res)
}
