package klient

import (
	"crypto/tls"
	"crypto/x509"
	"flag"
	"io/ioutil"
	"net/http"
	"testing"

	"github.com/myteksi/hystrix-go/hystrix"
	"github.com/stretchr/testify/assert"

	"gitlab.com/gx-regional/dakota/common/tracing"
	"gitlab.myteksi.net/gophers/go/commons/util/log/yall/slog"
)

func TestWithServiceName(t *testing.T) {
	serviceName := "test_service"
	var config Config
	WithServiceName(serviceName)(&config)
	assert.Equal(t, serviceName, config.ServiceName)
}

func TestWithEnableMockService(t *testing.T) {
	var config Config
	WithEnableMockService(true)(&config)
	assert.Equal(t, true, config.EnableMockService)
}

func TestWithCircuitConfig(t *testing.T) {
	circuitBreakerConf := map[string]hystrix.CommandConfig{
		"test_conf": {},
	}
	var config Config
	WithCircuitConfig(circuitBreakerConf)(&config)
	assert.Equal(t, circuitBreakerConf, config.CircuitConfig)
}

func TestWithEndpointCircuitConfig(t *testing.T) {
	circuitBreakerConf := map[string]hystrix.CommandConfig{
		"test_conf": {},
	}
	var config Config
	WithCircuitConfig(circuitBreakerConf)(&config)
	assert.Equal(t, circuitBreakerConf, config.CircuitConfig)
}

func TestWithLogger(t *testing.T) {
	logger := slog.Noop()
	var config Config
	WithLogger(logger)(&config)
	assert.Equal(t, logger, config.Logger)
}

func TestWithHTTPClient(t *testing.T) {
	httpClient := http.DefaultClient
	var config Config
	WithHTTPClient(httpClient)(&config)
	assert.Equal(t, httpClient, config.HTTPClient)
}

func TestWithTracing(t *testing.T) {
	tracer := tracing.DefaultTracer()
	var config Config
	WithTracing(tracer)(&config)
	assert.Equal(t, tracer, config.Tracer)
}

func TestWithTLSConfig(t *testing.T) {
	var config Config

	insecure := flag.Bool("insecure-ssl", false, "Accept/Ignore all server SSL certificates")
	// Get the SystemCertPool, continue with an empty pool on error
	rootCAs, _ := x509.SystemCertPool()
	if rootCAs == nil {
		rootCAs = x509.NewCertPool()
	}
	localCertFile := "./test/test_cert.crt"
	// Read in the cert file
	certs, err := ioutil.ReadFile(localCertFile)
	_ = assert.Nil(t, err)
	// Append our cert to the system pool
	appendErr := rootCAs.AppendCertsFromPEM(certs)
	_ = assert.True(t, appendErr)

	tlsConfig := &tls.Config{
		InsecureSkipVerify: *insecure,
		RootCAs:            rootCAs,
	}
	WithTLSConfig(tlsConfig)(&config)
	assert.Equal(t, tlsConfig, config.TLSConfig)
}

func TestWithNoHealthCheck(t *testing.T) {
	var config Config
	WithoutHealthCheck()(&config)
	assert.Equal(t, true, config.NoHealthCheck)
}
