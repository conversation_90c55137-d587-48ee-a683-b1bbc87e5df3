package rest

import (
	"context"
	"crypto/tls"
	"crypto/x509"
	"errors"
	"flag"
	"io/ioutil"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/hashicorp/go-multierror"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"

	"gitlab.com/gx-regional/dakota/klient"
	"gitlab.com/gx-regional/dakota/klient/mocks"
	"gitlab.com/gx-regional/dakota/servus/v2/statsd"
)

func TestRESTRoundTripper(t *testing.T) {
	testServer := httptest.NewServer(
		http.HandlerFunc(
			func(res http.ResponseWriter, req *http.Request) {
				require.Equal(t, http.MethodGet, req.Method)
				require.Equal(t, "/health_check", req.URL.Path)

				res.WriteHeader(http.StatusOK)
				_, err := res.Write([]byte("HealthCheck"))
				require.NoError(t, err)
			},
		),
	)
	defer testServer.Close()

	req := &mocks.Request{}
	res := &mocks.Response{}

	req.
		On("EncodeHTTPRequest", testServer.URL).
		Return(func(baseURL string) *http.Request {
			httpReq, err := http.NewRequest(http.MethodGet, baseURL+"/health_check", nil)
			require.NoError(t, err)
			return httpReq
		}, nil)

	res.
		On("DecodeHTTPResponse", mock.AnythingOfType("*http.Response")).
		Return(func(res *http.Response) error {
			bodyBytes, err := ioutil.ReadAll(res.Body)
			require.NoError(t, err)
			require.Equal(t, "HealthCheck", string(bodyBytes))
			return nil
		})

	insecure := flag.Bool("insecure-ssl", false, "Accept/Ignore all server SSL certificates")
	// Get the SystemCertPool, continue with an empty pool on error
	rootCAs, _ := x509.SystemCertPool()
	if rootCAs == nil {
		rootCAs = x509.NewCertPool()
	}
	localCertFile := "../test/test_cert.crt"
	// Read in the cert file
	certs, err := ioutil.ReadFile(localCertFile)
	assert.Nil(t, err)
	// Append our cert to the system pool
	appendErr := rootCAs.AppendCertsFromPEM(certs)
	assert.True(t, appendErr)

	tlsConfig := &tls.Config{
		InsecureSkipVerify: *insecure,
		RootCAs:            rootCAs,
	}
	rt := NewRoundTripper(http.DefaultClient, testServer.URL, nil, tlsConfig, true, true, statsd.NewNoop())
	rtErr := rt.RoundTrip(klient.MakeContext(context.Background(), &klient.EndpointDescriptor{}), req, res)
	require.NoError(t, rtErr)

	req.AssertExpectations(t)
	res.AssertExpectations(t)
}

func TestCheckedClose(t *testing.T) {
	t.Run("NoExistingErr", func(t *testing.T) {
		var err error
		closeErr := errors.New("close error")
		checkedClose(closeWithError(closeErr), &err)
		require.Equal(t, closeErr, err)
	})
	t.Run("WithExistingErr", func(t *testing.T) {
		err := errors.New("some other error")
		closeErr := errors.New("close error")
		checkedClose(closeWithError(closeErr), &err)
		require.IsType(t, &multierror.Error{}, err)
	})
}

type errorCloser struct {
	err error
}

func closeWithError(err error) *errorCloser {
	return &errorCloser{
		err: err,
	}
}

func (e *errorCloser) Close() error {
	return e.err
}
