// Package rest provides REST client implementation.
package rest

import (
	"context"
	"crypto/tls"
	"fmt"
	"io"
	"log"
	"net/http"
	"time"

	"github.com/hashicorp/go-multierror"

	commonCtx "gitlab.com/gx-regional/dakota/common/context"
	"gitlab.com/gx-regional/dakota/klient"
	"gitlab.com/gx-regional/dakota/klient/errorhandling"
	"gitlab.com/gx-regional/dakota/klient/utils"
	"gitlab.com/gx-regional/dakota/servus/v2"
	servusStatsD "gitlab.com/gx-regional/dakota/servus/v2/statsd"
	"gitlab.myteksi.net/gophers/go/commons/util/log/yall/slog"
	"gitlab.myteksi.net/gophers/go/commons/util/parallel/gconcurrent"
)

const (
	healthCheckLogTag = "healthCheck"

	// DefaultHealthCheckInterval specifies interval for dependency health check to happen
	DefaultHealthCheckInterval = time.Duration(5) * time.Second
)

var (
	commonHeaders = []string{
		commonCtx.HeaderXRequestID,
		commonCtx.HeaderXUserID,
		commonCtx.HeaderXServiceID,
		commonCtx.HeaderXServiceUserID,
		commonCtx.HeaderXClientID,
		commonCtx.HeaderIdempotencyKey,
		commonCtx.HeaderXJWTSessionData,
		commonCtx.HeaderTrueClientIP,
		commonCtx.HeaderXDeviceID,
		commonCtx.HeaderXAppSignature,
		commonCtx.HeaderXGrabHost,
		commonCtx.HeaderXProfileID,
		commonCtx.HeaderXProfileType,
		commonCtx.HeaderXAWSDeploymentMode, // new header for header based routing
	}
)

// HealthCheckRequestShell is a wrapper to make the object a klient.Request
type HealthCheckRequestShell http.Request

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (h *HealthCheckRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/health_check"
	fullURL := baseURL + filledPath

	req, err := http.NewRequest("GET", fullURL, nil)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// HealthCheckResponseShell is a wrapper to make the object a klient.Response
type HealthCheckResponseShell []byte

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (c *HealthCheckResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 200 && res.StatusCode < 300 {
		// _, _ = ioutil.ReadAll(res.Body)
		return nil
	} else if res.StatusCode == 503 {
		return fmt.Errorf("no healthy upstream")
	}
	return errorhandling.UnmarshalError(res)
}

// RoundTripper speaks HTTP with the server.
type RoundTripper struct {
	httpClient             *http.Client
	baseURL                string
	isHealthy              bool
	logger                 slog.YallLogger
	propagateCommonHeaders bool
	statsClient            servusStatsD.Client
}

// NewRoundTripper instantiates a new a RoundTripper that makes HTTP calls to baseURL and adds the tls config
func NewRoundTripper(httpClient *http.Client, baseURL string, logger slog.YallLogger, tlsConfig *tls.Config, isHealthCheckRequired, propagateCommonHeaders bool, statsClient servusStatsD.Client) *RoundTripper {
	if tlsConfig != nil {
		// add tls config if provided, dont override default transport settings
		transport, ok := httpClient.Transport.(*http.Transport)
		if ok {
			transport.TLSClientConfig = tlsConfig
		}
	}

	rt := &RoundTripper{
		httpClient:             httpClient,
		baseURL:                baseURL,
		logger:                 logger,
		propagateCommonHeaders: propagateCommonHeaders,
		statsClient:            statsClient,
	}
	if isHealthCheckRequired {
		gconcurrent.Schedule(DefaultHealthCheckInterval, true, IsDependencyHealthy, context.Background(), rt)
	}
	return rt
}

// RoundTrip encodes the request DTO into *http.Request, makes the HTTP call, and then decodes *http.Response to the response DTO.
func (r *RoundTripper) RoundTrip(ctx context.Context, req klient.Request, res klient.Response) (err error) {
	httpReq, err := req.EncodeHTTPRequest(r.baseURL)
	if err != nil {
		return err
	}
	// get header information, if any, from the context and add in the http request
	addHTTPHeaders(ctx, httpReq)
	if r.propagateCommonHeaders {
		propagateCommonHeaders(ctx, httpReq)
	}
	httpReq = httpReq.WithContext(ctx)

	httpRes, err := r.httpClient.Do(httpReq)
	if err != nil {
		return err
	}
	defer checkedClose(httpRes.Body, &err)
	exportResponseMetrics(ctx, httpRes, r.statsClient)
	return res.DecodeHTTPResponse(httpRes)
}

func exportResponseMetrics(ctx context.Context, httpRes *http.Response, statsClient servusStatsD.Client) {
	if httpRes != nil {
		desc := klient.DescribeEndpoint(ctx)
		statsTags := append(make([]string, 0), "to_service:"+desc.Service)
		statsTags = append(statsTags, "endpoint:"+desc.Name)
		statsTags = append(statsTags, fmt.Sprintf("response_code:%d", httpRes.StatusCode))
		sourceServiceName := utils.GetSourceServiceName()
		if sourceServiceName != "" {
			statsTags = append(statsTags, "from_service:"+sourceServiceName)
		}
		statsClient.Count1("klient.http", "response_code", statsTags...)
	}
}

// IsDependencyHealthy checks if dependency service is healthy or not.
func IsDependencyHealthy(ctx context.Context, r *RoundTripper) {
	req := &HealthCheckRequestShell{}
	res := &HealthCheckResponseShell{}
	httpReq, encodeErr := req.EncodeHTTPRequest(r.baseURL)
	if encodeErr != nil {
		r.isHealthy = false
		log.Printf("unable to encode request while health check for %s: %s\n", r.baseURL, encodeErr.Error())
		// r.logger.Warn(healthCheckLogTag, fmt.Sprintf("unable to encode request while health check: %s\n", encodeErr.Error()))
		return
	}

	httpReq = httpReq.WithContext(ctx)
	httpRes, err := r.httpClient.Do(httpReq)
	if err != nil {
		r.isHealthy = false
		log.Printf("unable to establish connection with %s: %s\n", r.baseURL, err.Error())
		// r.logger.Warn(healthCheckLogTag, fmt.Sprintf("unable to establish connection with %s: %s\n", r.baseURL, err.Error()))
		return
	}
	defer checkedClose(httpRes.Body, &err)

	decodeErr := res.DecodeHTTPResponse(httpRes)
	if decodeErr != nil {
		r.isHealthy = false
		log.Printf("unable to decode response while health check for %s: %s\n", r.baseURL, decodeErr.Error())
		// r.logger.Warn(healthCheckLogTag, fmt.Sprintf("unable to decode response while health check: %s\n", decodeErr.Error()))
		return
	}
	r.isHealthy = true
}

func checkedClose(closer io.Closer, err *error) {
	closeErr := closer.Close()
	if closeErr != nil {
		if *err != nil {
			*err = multierror.Append(*err, closeErr)
			return
		}
		*err = closeErr
	}
}

func addHTTPHeaders(ctx context.Context, req *http.Request) {
	reqHeaders := commonCtx.ListHTTPHeader(ctx)
	for k, v := range reqHeaders {
		req.Header.Add(k, v)
	}
}

func propagateCommonHeaders(ctx context.Context, req *http.Request) {
	for _, header := range commonHeaders {
		if req.Header.Get(header) != "" {
			continue
		}
		existingHeader := servus.HeaderFromCtx(ctx).Get(header)
		if existingHeader != "" {
			req.Header.Add(header, existingHeader)
		}
	}
}
