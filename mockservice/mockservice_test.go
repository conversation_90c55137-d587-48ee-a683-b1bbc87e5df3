package mockservice

import (
	"context"
	"errors"
	"net/http"
	"os"
	"testing"

	"gitlab.com/gx-regional/dakota/klient"
)

func TestMiddlewareCreationWithDefaultHost(t *testing.T) {
	httpClient := &http.Client{}
	baseURL := "http://example.com"

	middleware := NewMiddleware(httpClient, baseURL, nil, false, nil)

	expectedBaseURL := "http://mock-service.backend-dakota-app-01.svc.cluster.local/example.com"
	if middleware.baseURL != expectedBaseURL {
		t.Errorf("Unexpected baseURL: got %s want %s", middleware.baseURL, expectedBaseURL)
	}
}

func TestMiddlewareCreationWithEnvHost(t *testing.T) {
	httpClient := &http.Client{}
	baseURL := "http://example.com"

	os.Setenv("MOCK_SERVICE_HOST", "http://mock-service-host-from-env")
	defer os.Unsetenv("MOCK_SERVICE_HOST")

	middleware := NewMiddleware(httpClient, baseURL, nil, false, nil)

	expectedBaseURL := "http://mock-service-host-from-env/example.com"
	if middleware.baseURL != expectedBaseURL {
		t.Errorf("Unexpected baseURL: got %s want %s", middleware.baseURL, expectedBaseURL)
	}
}

type mockRoundTripper struct {
	err error
}

func (m mockRoundTripper) RoundTrip(ctx context.Context, req klient.Request, res klient.Response) error {
	return m.err
}

type mockRequest struct{}

func (m mockRequest) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	return &http.Request{}, nil
}

type mockResponse struct{}

func (m mockResponse) DecodeHTTPResponse(res *http.Response) error {
	return nil
}

func TestRoundTripper_RoundTrip(t *testing.T) {
	mockError := errors.New(mockNoMatchCode)
	next := mockRoundTripper{err: errors.New("next error")}

	roundTripper := RoundTripper{
		mockHTTPRoundTripper: mockRoundTripper{err: mockError},
		next:                 next,
	}

	req := mockRequest{}
	res := mockResponse{}

	err := roundTripper.RoundTrip(context.Background(), req, res)
	if err != next.err {
		t.Errorf("Expected error from next round tripper, got: %v", err)
	}
}

func TestRoundTripper_RoundTrip_NoMockError(t *testing.T) {
	mockError := errors.New("mock error")
	roundTripper := RoundTripper{
		mockHTTPRoundTripper: mockRoundTripper{err: mockError},
		next:                 mockRoundTripper{},
	}

	req := mockRequest{}
	res := mockResponse{}

	err := roundTripper.RoundTrip(context.Background(), req, res)
	if err != mockError {
		t.Errorf("Expected error from mock round tripper, got: %v", err)
	}
}

func TestMiddleware_Apply(t *testing.T) {
	middleware := Middleware{
		httpClient: &http.Client{},
		baseURL:    "http://example.com",
	}

	rt := mockRoundTripper{}
	result := middleware.Apply(rt)

	_, ok := result.(*RoundTripper)
	if !ok {
		t.Errorf("Expected result to be of type *RoundTripper, got: %T", result)
	}
}
