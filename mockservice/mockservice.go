// Package mockservice provides the Middleware and RoundTripper to send requests to mock service.
package mockservice

import (
	"context"
	"crypto/tls"
	"fmt"
	"net/http"
	"os"
	"strings"

	"gitlab.com/gx-regional/dakota/klient"
	"gitlab.com/gx-regional/dakota/klient/errorhandling"
	"gitlab.com/gx-regional/dakota/klient/rest"
	servusStatsD "gitlab.com/gx-regional/dakota/servus/v2/statsd"
	"gitlab.myteksi.net/gophers/go/commons/util/log/yall/slog"
)

const (
	defaultMockServiceHost = "http://mock-service.backend-dakota-app-01.svc.cluster.local"
	mockNoMatchCode        = "MOCK_NO_MATCH"
	mockHostEnv            = "MOCK_SERVICE_HOST"
)

// Middleware enhances klient.RoundTripper with mock capability.
type Middleware struct {
	httpClient             *http.Client
	baseURL                string
	logger                 slog.YallLogger
	tlsConfig              *tls.Config
	isHealthCheckRequired  bool
	propagateCommonHeaders bool
	statsClient            servusStatsD.Client
}

// NewMiddleware instantiates a new mock service Middleware.
func NewMiddleware(httpClient *http.Client, baseURL string, logger slog.YallLogger, propagateCommonHeaders bool, statsClient servusStatsD.Client) *Middleware {
	baseURL = strings.TrimPrefix(baseURL, "http://")
	baseURL = strings.TrimPrefix(baseURL, "https://")
	mockServiceHost := os.Getenv(mockHostEnv)
	if mockServiceHost == "" {
		mockServiceHost = defaultMockServiceHost
	}
	mockURL := fmt.Sprintf("%s/%s", mockServiceHost, baseURL)
	return &Middleware{
		httpClient:             httpClient,
		baseURL:                mockURL,
		logger:                 logger,
		tlsConfig:              nil,
		isHealthCheckRequired:  false,
		propagateCommonHeaders: propagateCommonHeaders,
		statsClient:            statsClient,
	}
}

// RoundTripper mock-service round-trip.
type RoundTripper struct {
	mockHTTPRoundTripper klient.RoundTripper
	next                 klient.RoundTripper
}

// RoundTrip sends a request to mock service.
func (r RoundTripper) RoundTrip(ctx context.Context, req klient.Request, res klient.Response) error {
	err := r.mockHTTPRoundTripper.RoundTrip(ctx, req, res)
	if err != nil {
		mockErrorCode := errorhandling.ErrorCode(err)
		if mockErrorCode == mockNoMatchCode {
			return r.next.RoundTrip(ctx, req, res)
		}
	}
	return err
}

// Apply applies the Middleware to a klient.RoundTripper.
func (m *Middleware) Apply(rt klient.RoundTripper) klient.RoundTripper {
	mockHTTPRoundTripper := rest.NewRoundTripper(m.httpClient, m.baseURL, m.logger, m.tlsConfig, m.isHealthCheckRequired, m.propagateCommonHeaders, m.statsClient)
	return &RoundTripper{
		mockHTTPRoundTripper: mockHTTPRoundTripper,
		next:                 rt,
	}
}
