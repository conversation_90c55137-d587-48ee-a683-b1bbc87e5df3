// Code generated by mockery v0.0.0-dev. DO NOT EDIT.

package mocks

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
	klient "gitlab.com/gx-regional/dakota/klient"
)

// RoundTripper is an autogenerated mock type for the RoundTripper type
type RoundTripper struct {
	mock.Mock
}

// RoundTrip provides a mock function with given fields: ctx, req, res
func (_m *RoundTripper) RoundTrip(ctx context.Context, req klient.Request, res klient.Response) error {
	ret := _m.Called(ctx, req, res)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, klient.Request, klient.Response) error); ok {
		r0 = rf(ctx, req, res)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}
