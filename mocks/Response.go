// Code generated by mockery v0.0.0-dev. DO NOT EDIT.

package mocks

import (
	http "net/http"

	mock "github.com/stretchr/testify/mock"
)

// Response is an autogenerated mock type for the Response type
type Response struct {
	mock.Mock
}

// DecodeHTTPResponse provides a mock function with given fields: res
func (_m *Response) DecodeHTTPResponse(res *http.Response) error {
	ret := _m.Called(res)

	var r0 error
	if rf, ok := ret.Get(0).(func(*http.Response) error); ok {
		r0 = rf(res)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}
