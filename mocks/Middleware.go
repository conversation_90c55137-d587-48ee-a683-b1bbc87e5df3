// Code generated by mockery v0.0.0-dev. DO NOT EDIT.

package mocks

import (
	mock "github.com/stretchr/testify/mock"
	klient "gitlab.com/gx-regional/dakota/klient"
)

// Middleware is an autogenerated mock type for the Middleware type
type Middleware struct {
	mock.Mock
}

// Apply provides a mock function with given fields: rt
func (_m *Middleware) Apply(rt klient.RoundTripper) klient.RoundTripper {
	ret := _m.Called(rt)

	var r0 klient.RoundTripper
	if rf, ok := ret.Get(0).(func(klient.RoundTripper) klient.RoundTripper); ok {
		r0 = rf(rt)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(klient.RoundTripper)
		}
	}

	return r0
}
