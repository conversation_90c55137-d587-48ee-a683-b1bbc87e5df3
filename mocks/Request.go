// Code generated by mockery v0.0.0-dev. DO NOT EDIT.

package mocks

import (
	http "net/http"

	mock "github.com/stretchr/testify/mock"
)

// Request is an autogenerated mock type for the Request type
type Request struct {
	mock.Mock
}

// EncodeHTTPRequest provides a mock function with given fields: baseURL
func (_m *Request) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	ret := _m.Called(baseURL)

	var r0 *http.Request
	if rf, ok := ret.Get(0).(func(string) *http.Request); ok {
		r0 = rf(baseURL)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*http.Request)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(baseURL)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}
